import { apiRequest } from './api';
import { 
  Project, 
  WorkOrder, 
  PlanTask, 
  ExecutionLog, 
  DashboardStats,
  PaginatedResponse,
  PaginationParams 
} from '../types';

// 项目服务
export const projectService = {
  // 获取项目列表
  getProjects: (params?: PaginationParams) => 
    apiRequest.get<PaginatedResponse<Project>>('/projects', params),

  // 获取项目详情
  getProject: (id: number) => 
    apiRequest.get<Project>(`/projects/${id}`),

  // 创建项目
  createProject: (data: Partial<Project>) => 
    apiRequest.post<Project>('/projects', data),

  // 更新项目
  updateProject: (id: number, data: Partial<Project>) => 
    apiRequest.put<Project>(`/projects/${id}`, data),

  // 删除项目
  deleteProject: (id: number) => 
    apiRequest.delete(`/projects/${id}`),
};

// 工单服务
export const workOrderService = {
  // 获取工单列表
  getWorkOrders: (params?: PaginationParams & { status?: string; project_id?: number }) => 
    apiRequest.get<PaginatedResponse<WorkOrder>>('/work-orders', params),

  // 获取工单详情
  getWorkOrder: (id: number) => 
    apiRequest.get<WorkOrder>(`/work-orders/${id}`),

  // 创建工单
  createWorkOrder: (data: Partial<WorkOrder>) => 
    apiRequest.post<WorkOrder>('/work-orders', data),

  // 更新工单
  updateWorkOrder: (id: number, data: Partial<WorkOrder>) => 
    apiRequest.put<WorkOrder>(`/work-orders/${id}`, data),

  // 删除工单
  deleteWorkOrder: (id: number) => 
    apiRequest.delete(`/work-orders/${id}`),

  // 启动工单
  startWorkOrder: (id: number) => 
    apiRequest.post(`/work-orders/${id}/start`),

  // 完成工单
  completeWorkOrder: (id: number) => 
    apiRequest.post(`/work-orders/${id}/complete`),
};

// 计划任务服务
export const planTaskService = {
  // 获取任务列表
  getTasks: (params?: PaginationParams & { work_order_id?: number; status?: string }) => 
    apiRequest.get<PaginatedResponse<PlanTask>>('/plan-tasks', params),

  // 获取任务详情
  getTask: (id: number) => 
    apiRequest.get<PlanTask>(`/plan-tasks/${id}`),

  // 创建任务
  createTask: (data: Partial<PlanTask>) => 
    apiRequest.post<PlanTask>('/plan-tasks', data),

  // 更新任务
  updateTask: (id: number, data: Partial<PlanTask>) => 
    apiRequest.put<PlanTask>(`/plan-tasks/${id}`, data),

  // 删除任务
  deleteTask: (id: number) => 
    apiRequest.delete(`/plan-tasks/${id}`),

  // 自动调度任务
  autoSchedule: (workOrderId: number) => 
    apiRequest.post(`/plan-tasks/auto-schedule`, { work_order_id: workOrderId }),

  // 获取我的任务
  getMyTasks: () => 
    apiRequest.get<PlanTask[]>('/execution/my-tasks'),
};

// 执行服务
export const executionService = {
  // 开始任务
  startTask: (taskId: number, notes?: string) => 
    apiRequest.post(`/execution/start/${taskId}`, { notes }),

  // 暂停任务
  pauseTask: (taskId: number, notes?: string) => 
    apiRequest.post(`/execution/pause/${taskId}`, { notes }),

  // 恢复任务
  resumeTask: (taskId: number, notes?: string) => 
    apiRequest.post(`/execution/resume/${taskId}`, { notes }),

  // 完成任务
  completeTask: (taskId: number, notes?: string, qualityData?: any) => 
    apiRequest.post(`/execution/complete/${taskId}`, { notes, quality_data: qualityData }),

  // 报告问题
  reportIssue: (taskId: number, notes: string, attachments?: string[]) => 
    apiRequest.post(`/execution/issue/${taskId}`, { notes, attachments }),

  // 获取执行日志
  getExecutionLogs: (taskId: number) => 
    apiRequest.get<ExecutionLog[]>(`/execution/logs/${taskId}`),

  // 获取工作站任务
  getWorkstationTasks: (workstationId?: number) => 
    apiRequest.get<PlanTask[]>('/execution/workstation-tasks', { workstation_id: workstationId }),

  // 获取执行摘要
  getExecutionSummary: () => 
    apiRequest.get('/execution/summary'),
};

// 仪表板服务
export const dashboardService = {
  // 获取仪表板统计数据
  getStats: () => 
    apiRequest.get<DashboardStats>('/analytics/dashboard'),

  // 获取生产效率数据
  getEfficiencyData: (startDate: string, endDate: string) => 
    apiRequest.get('/analytics/efficiency', { start_date: startDate, end_date: endDate }),

  // 获取质量数据
  getQualityData: (startDate: string, endDate: string) => 
    apiRequest.get('/analytics/quality', { start_date: startDate, end_date: endDate }),

  // 获取设备利用率数据
  getEquipmentData: (startDate: string, endDate: string) => 
    apiRequest.get('/analytics/equipment', { start_date: startDate, end_date: endDate }),

  // 获取操作员绩效数据
  getOperatorData: (startDate: string, endDate: string) => 
    apiRequest.get('/analytics/operator', { start_date: startDate, end_date: endDate }),
};

// 系统服务
export const systemService = {
  // 获取系统健康状态
  getSystemHealth: () =>
    apiRequest.get('/system/health'),

  // 获取系统指标
  getSystemMetrics: () =>
    apiRequest.get('/system/metrics'),

  // 获取审计日志
  getAuditLogs: (params?: PaginationParams) =>
    apiRequest.get('/system/audit-logs', params),

  // 获取系统设置
  getSettings: () =>
    apiRequest.get('/system/settings'),

  // 更新系统设置
  updateSettings: (data: any) =>
    apiRequest.put('/system/settings', data),
};

// 用户服务
export const userService = {
  getUsers: () => apiRequest.get('/users'),
  createUser: (data: any) => apiRequest.post('/users', data),
  updateUser: (id: number, data: any) => apiRequest.put(`/users/${id}`, data),
  deleteUser: (id: number) => apiRequest.delete(`/users/${id}`),
  updateUserStatus: (id: number, isActive: boolean) => apiRequest.put(`/users/${id}/status`, { is_active: isActive }),
};

// 零件服务
export const partService = {
  getParts: (params?: any) => apiRequest.get('/parts', { params }),
  createPart: (data: any) => apiRequest.post('/parts', data),
  updatePart: (id: number, data: any) => apiRequest.put(`/parts/${id}`, data),
  deletePart: (id: number) => apiRequest.delete(`/parts/${id}`),
  getPartById: (id: number) => apiRequest.get(`/parts/${id}`),
};
